<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.text.Font?>


<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="600.0" prefWidth="900.0" xmlns="http://javafx.com/javafx/19" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <AnchorPane prefHeight="600.0" prefWidth="450.0" style="-fx-background-color: e1f8ff;">
         <children>
            <ImageView fitHeight="150.0" fitWidth="200.0" layoutX="125.0" layoutY="150.0" pickOnBounds="true" preserveRatio="true">
               <image>
                  <Image url="@../assets/images/Logo.png" />
               </image>
            </ImageView>
            <Label layoutX="93.0" layoutY="341.0" prefHeight="38.0" prefWidth="264.0" text="An Online Media Store" textAlignment="CENTER" textFill="#e80a0a">
               <font>
                  <Font name="Forte" size="26.0" />
               </font>
            </Label>
         </children>
      </AnchorPane>
      <AnchorPane layoutX="450.0" prefHeight="600.0" prefWidth="450.0">
         <children>
            <Label layoutX="96.0" layoutY="84.0" prefHeight="42.0" prefWidth="258.0" text="LOGIN ACCOUNT" textAlignment="CENTER" textFill="#e80a0a">
               <font>
                  <Font name="Arial Bold" size="30.0" />
               </font>
            </Label>
            <GridPane layoutX="36.0" layoutY="186.0" prefHeight="150.0" prefWidth="378.0">
              <columnConstraints>
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="183.0" minWidth="10.0" prefWidth="125.0" />
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="291.0" minWidth="10.0" prefWidth="253.0" />
              </columnConstraints>
              <rowConstraints>
                <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
              </rowConstraints>
               <children>
                  <Label prefHeight="32.0" prefWidth="89.0" text="Username :" textFill="#e80a0a">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </Label>
                  <Label prefHeight="32.0" prefWidth="89.0" text="Password :" textFill="#e80a0a" GridPane.rowIndex="1">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </Label>
                  <TextField fx:id="username" GridPane.columnIndex="1">
                     <font>
                        <Font size="16.0" />
                     </font>
                  </TextField>
                  <PasswordField fx:id="password" GridPane.columnIndex="1" GridPane.rowIndex="1">
                     <font>
                        <Font size="16.0" />
                     </font>
                  </PasswordField>
               </children>
            </GridPane>
            <Button fx:id="loginBtn" layoutX="159.0" layoutY="367.0" mnemonicParsing="false" prefHeight="35.0" prefWidth="133.0" text="Login" textFill="#e80a0a">
               <font>
                  <Font size="16.0" />
               </font>
               <cursor>
                  <Cursor fx:constant="HAND" />
               </cursor>
            </Button>
            <Hyperlink fx:id="homeHyperLink" layoutX="172.0" layoutY="560.0" text="Login as guest?" underline="true">
               <font>
                  <Font size="14.0" />
               </font>
            </Hyperlink>
         </children>
      </AnchorPane>
   </children>
</AnchorPane>
