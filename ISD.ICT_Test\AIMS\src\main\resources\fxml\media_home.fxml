<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Spinner?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="150.0" prefWidth="253.0" style="-fx-border-color: #ccd9ff;" xmlns="http://javafx.com/javafx/19" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <ImageView fx:id="mediaImage" fitHeight="132.0" fitWidth="99.0" layoutX="14.0" layoutY="10.0" pickOnBounds="true" preserveRatio="true">
         <image>
            <Image url="@../assets/images/1.png" />
         </image>
      </ImageView>
      <Pane layoutX="135.0" layoutY="-1.0" prefHeight="150.0" prefWidth="118.0">
         <children>
            <VBox prefHeight="19.0" prefWidth="116.0">
               <children>
                  <Label fx:id="mediaTitle" alignment="CENTER" prefHeight="12.0" prefWidth="116.0" text="  Title" textAlignment="CENTER" wrapText="true">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
               </children>
            </VBox>
            <VBox layoutY="27.0" prefHeight="120.0" prefWidth="118.0" style="-fx-border-color: #80ff80;">
               <children>
                  <HBox alignment="CENTER" prefHeight="24.0" prefWidth="84.8">
                     <children>
                        <Pane prefHeight="33.75" prefWidth="44.0">
                           <children>
                              <Label alignment="CENTER" layoutX="4.0" layoutY="7.5" prefHeight="12.0" prefWidth="40.8" text="Price">
                                 <font>
                                    <Font name="System Bold Italic" size="13.0" />
                                 </font>
                              </Label>
                           </children>
                        </Pane>
                        <Pane prefHeight="31.5" prefWidth="48.8">
                           <children>
                              <Label fx:id="mediaPrice" layoutX="1.0" layoutY="8.0" prefHeight="17.0" prefWidth="55.0" text="100" />
                           </children>
                        </Pane>
                     </children>
                  </HBox>
                  <HBox prefHeight="22.5" prefWidth="84.8">
                     <children>
                        <Pane prefHeight="30.0" prefWidth="44.8">
                           <children>
                              <Label alignment="CENTER" layoutX="10.0" layoutY="2.0" prefHeight="19.0" prefWidth="49.0" text="Avail">
                                 <font>
                                    <Font name="System Bold Italic" size="13.0" />
                                 </font>
                              </Label>
                           </children>
                        </Pane>
                        <Pane prefHeight="22.125" prefWidth="48.0">
                           <children>
                              <Label fx:id="mediaAvail" layoutX="24.0" layoutY="3.0" prefHeight="17.0" prefWidth="49.0" text="9" />
                           </children>
                        </Pane>
                     </children>
                  </HBox>
                  <HBox alignment="CENTER" prefHeight="24.0" prefWidth="84.8">
                     <children>
                        <Spinner fx:id="spinnerChangeNumber" prefHeight="19.5" prefWidth="78.4" />
                     </children>
                  </HBox>
                  <HBox alignment="CENTER" prefHeight="54.0" prefWidth="84.8">
                     <children>
                        <Button fx:id="addToCartBtn" alignment="CENTER" mnemonicParsing="false" prefHeight="26.25" prefWidth="77.6" style="-fx-cursor: hand;" text="Add to Cart" />
                     </children>
                  </HBox>
               </children>
            </VBox>
         </children></Pane>
   </children>
</AnchorPane>
