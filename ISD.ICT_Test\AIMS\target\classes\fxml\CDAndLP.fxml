<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.RadioButton?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.control.ToggleGroup?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.text.Font?>

<StackPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="400.0" prefWidth="600.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.controller.productmanager.CDAndLPScreen">
    <children>
        <AnchorPane prefHeight="200.0" prefWidth="200.0">
            <children>
                <AnchorPane layoutX="-3.0" prefHeight="400.0" prefWidth="300.0">
                    <children>
                        <Label layoutX="21.0" layoutY="115.0" text="Artists" AnchorPane.leftAnchor="18.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="cdAndLp_artists" layoutX="113.0" layoutY="110.0" prefHeight="30.0" prefWidth="180.0" />
                        <Label layoutX="21.0" layoutY="198.0" text="Record label" AnchorPane.leftAnchor="18.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="cdAndLp_recordLabel" layoutX="113.0" layoutY="193.0" prefHeight="30.0" prefWidth="180.0" />
                        <Label layoutX="21.0" layoutY="272.0" text="Track list" AnchorPane.leftAnchor="18.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="cdAndLp_trackList" layoutX="113.0" layoutY="267.0" prefHeight="30.0" prefWidth="180.0" />
                    </children>
                </AnchorPane>
                <AnchorPane fx:id="loginForm" layoutX="300.0" prefHeight="400.0" prefWidth="300.0">
                    <children>
                        <Button fx:id="addCDAndLPBtn" layoutX="-24.0" layoutY="360.0" mnemonicParsing="false" onAction="#addCDAndLPBtnAction" text="Add" />
                        <Label fx:id="CDAndLPDetailLabel" layoutX="-66.0" layoutY="14.0" text="Add CD/LP detail">
                            <font>
                                <Font name="System Bold" size="20.0" />
                            </font>
                        </Label>
                        <DatePicker fx:id="cdAndLp_releaseDate" layoutX="113.0" layoutY="111.0" prefHeight="30.0" prefWidth="180.0" />
                        <Label layoutY="115.0" text="Publication date">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <Label layoutX="6.0" layoutY="199.0" text="Music genre">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="cdAndLp_genre" layoutX="114.0" layoutY="193.0" prefHeight="30.0" prefWidth="180.0" />
                  <HBox alignment="CENTER" layoutX="11.0" layoutY="262.0" prefHeight="42.0" prefWidth="278.0" spacing="20.0" AnchorPane.bottomAnchor="95.60000000000002" AnchorPane.leftAnchor="11.0" AnchorPane.rightAnchor="10.599999999999966" AnchorPane.topAnchor="262.0">
                     <children>
                        <RadioButton fx:id="cdAndLp_isCD" mnemonicParsing="false" selected="true" text="CD" HBox.hgrow="ALWAYS">
                           <font>
                              <Font size="15.0" />
                           </font>
                           <toggleGroup>
                              <ToggleGroup fx:id="CDorLP" />
                           </toggleGroup>
                        </RadioButton>
                        <RadioButton fx:id="cdAndLp_isLP" mnemonicParsing="false" text="LP" toggleGroup="$CDorLP" HBox.hgrow="ALWAYS">
                           <font>
                              <Font size="15.0" />
                           </font>
                        </RadioButton>
                     </children>
                  </HBox>
                    </children>
                </AnchorPane>
            </children>
        </AnchorPane>
    </children>
</StackPane>
