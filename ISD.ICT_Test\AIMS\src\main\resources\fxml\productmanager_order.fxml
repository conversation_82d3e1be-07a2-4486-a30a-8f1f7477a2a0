<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>


<AnchorPane maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.controller.productmanager.ProductManagerOrderController">
   <children>
      <AnchorPane layoutX="58.0" layoutY="135.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <HBox spacing="20.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <VBox spacing="20.0" HBox.hgrow="ALWAYS">
                     <children>
                        <TableView fx:id="orders_tableView" onMouseClicked="#selectOrder" prefHeight="340.0">
                          <columns>
                            <TableColumn fx:id="orders_col_id" editable="false" minWidth="-Infinity" prefWidth="30.0" text="ID" />
                              <TableColumn fx:id="orders_col_subtotal" editable="false" minWidth="-Infinity" prefWidth="50.0" text="Subtotal" />
                              <TableColumn fx:id="orders_col_shippingFee" editable="false" minWidth="-Infinity" prefWidth="50.0" text="Shipping fee" />
                              <TableColumn fx:id="orders_col_type" editable="false" minWidth="-Infinity" prefWidth="50.0" text="Order type" />
                              <TableColumn fx:id="orders_col_status" editable="false" minWidth="-Infinity" prefWidth="50.0" text="Status" />
                          </columns>
                           <columnResizePolicy>
                              <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                           </columnResizePolicy>
                        </TableView>
                        <HBox alignment="CENTER" fillHeight="false" spacing="40.0">
                           <children>
                              <Button mnemonicParsing="false" onAction="#acceptOrder" prefWidth="80.0" text="Accept" HBox.hgrow="ALWAYS">
                                 <font>
                                    <Font size="18.0" />
                                 </font>
                              </Button>
                              <Button mnemonicParsing="false" onAction="#rejectOrder" prefWidth="80.0" text="Reject">
                                 <font>
                                    <Font size="18.0" />
                                 </font>
                              </Button>
                           </children>
                        </HBox>
                        <Label text="Delivery Info">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                           <padding>
                              <Insets left="20.0" />
                           </padding>
                        </Label>
                        <GridPane>
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="347.199951171875" minWidth="10.0" prefWidth="109.6" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="439.19999999999993" minWidth="10.0" prefWidth="439.19999999999993" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="10.0" prefHeight="120.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Name:">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Phone:" GridPane.rowIndex="1">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Province:" GridPane.rowIndex="2">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Address:" GridPane.rowIndex="3">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Email:" GridPane.rowIndex="4">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label alignment="TOP_LEFT" layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Instruction:" GridPane.rowIndex="5">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                                 <GridPane.margin>
                                    <Insets />
                                 </GridPane.margin>
                                 <padding>
                                    <Insets top="10.0" />
                                 </padding>
                              </Label>
                              <Label fx:id="delivery_name" layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Unknown" GridPane.columnIndex="1">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label fx:id="delivery_phone" layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Unknown" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label fx:id="delivery_province" layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Unknown" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label fx:id="delivery_address" layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Unknown" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label fx:id="delivery_email" layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Unknown" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label fx:id="delivery_instruction" alignment="TOP_LEFT" layoutX="10.0" layoutY="10.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Unknown" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                                 <GridPane.margin>
                                    <Insets />
                                 </GridPane.margin>
                                 <padding>
                                    <Insets top="10.0" />
                                 </padding>
                              </Label>
                           </children>
                           <padding>
                              <Insets left="20.0" />
                           </padding>
                        </GridPane>
                     </children>
                  </VBox>
                  <VBox prefWidth="500.0" spacing="20.0" HBox.hgrow="ALWAYS">
                     <children>
                        <TableView fx:id="omedias_tableView" prefHeight="340.0">
                          <columns>
                            <TableColumn fx:id="omedias_col_id" editable="false" minWidth="30.0" prefWidth="30.0" text="ID" />
                            <TableColumn fx:id="omedias_col_title" editable="false" prefWidth="100.0" text="Media" />
                              <TableColumn fx:id="omedias_col_quantity" editable="false" minWidth="30.0" prefWidth="30.0" text="Quantity" />
                              <TableColumn fx:id="omedias_col_orderType" editable="false" minWidth="50.0" prefWidth="50.0" text="Order type" />
                          </columns>
                           <columnResizePolicy>
                              <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                           </columnResizePolicy>
                        </TableView>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </HBox>
         </children>
      </AnchorPane>
   </children>
</AnchorPane>
