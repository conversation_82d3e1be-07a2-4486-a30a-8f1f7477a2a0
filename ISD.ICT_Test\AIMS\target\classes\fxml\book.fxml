<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.text.Font?>

<StackPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="400.0" prefWidth="600.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.controller.productmanager.BookScreen">
    <children>
        <AnchorPane prefHeight="200.0" prefWidth="200.0">
            <children>
                <AnchorPane layoutX="-3.0" prefHeight="400.0" prefWidth="300.0">
               <children>
                  <Label layoutX="21.0" layoutY="115.0" text="Author">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                        <TextField fx:id="book_author" layoutX="98.0" layoutY="110.0" prefHeight="30.0" prefWidth="180.0" />
                  <Label layoutX="21.0" layoutY="198.0" text="HardCover">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="book_coverType" layoutX="99.0" layoutY="193.0" prefHeight="30.0" prefWidth="180.0" />
                  <Label layoutX="21.0" layoutY="272.0" text="Publisher">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="book_publisher" layoutX="99.0" layoutY="267.0" prefHeight="30.0" prefWidth="180.0" />
               </children>
                </AnchorPane>
                <AnchorPane fx:id="loginForm" layoutX="300.0" prefHeight="400.0" prefWidth="300.0">
                    <children>
                        <Button fx:id="addBookBtn" layoutX="-24.0" layoutY="360.0" mnemonicParsing="false" onAction="#addBookBtnAction" text="Add" />
                        <Label fx:id="bookDetailLabel" layoutX="-66.0" layoutY="14.0" text="Add Book detail">
                            <font>
                                <Font name="System Bold" size="20.0" />
                            </font>
                        </Label>
                  <DatePicker fx:id="book_publicationDate" layoutX="113.0" layoutY="111.0" prefHeight="30.0" prefWidth="180.0" />
                  <Label layoutX="5.0" layoutY="116.0" text="Publication date">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <Label layoutX="6.0" layoutY="199.0" text="Language">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="book_language" layoutX="114.0" layoutY="193.0" prefHeight="30.0" prefWidth="180.0" />
                  <Label layoutX="6.0" layoutY="271.0" text="Genre">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="book_genre" layoutX="114.0" layoutY="265.0" prefHeight="30.0" prefWidth="180.0" />
                  <Label layoutX="5.0" layoutY="62.0" text="Pages">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="book_pages" layoutX="113.0" layoutY="56.0" prefHeight="30.0" prefWidth="50.0" />
                    </children>
                </AnchorPane>
            </children>
        </AnchorPane>
    </children>
</StackPane>
