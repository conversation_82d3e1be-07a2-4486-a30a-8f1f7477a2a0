<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.*?>
<?import javafx.collections.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0" xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1">
    <children>
        <HBox prefHeight="104.0" prefWidth="1326.0" style="-fx-background-color: #ccebff; -fx-border-color: #33adff;">
            <children>
                <VBox prefHeight="102.0" prefWidth="231.0">
                    <children>
                        <Pane prefHeight="102.0" prefWidth="237.0">
                            <children>
                                <ImageView fx:id="aimsImage" fitHeight="94.0" fitWidth="84.0" layoutY="1.0" pickOnBounds="true" preserveRatio="true" style="-fx-cursor: hand;">
                                    <image>
                                        <Image url="@../assets/images/Logo.png" />
                                    </image>
                                </ImageView>
                            </children>
                        </Pane>
                    </children>
                </VBox>
                <HBox prefHeight="102.0" prefWidth="791.0">
                    <children>
                        <Pane prefHeight="102.0" prefWidth="562.0">
                            <children>
                                <TextField fx:id="searchTextField" layoutX="17.0" layoutY="27.0" prefHeight="48.0" prefWidth="407.0">
                                    <font>
                                        <Font size="18.0" />
                                    </font>
                                </TextField>
                            </children>
                        </Pane>
                        <Pane prefHeight="102.0" prefWidth="562.0">
                            <children>
                                <ChoiceBox fx:id="choiceBoxOrder" layoutX="164.0" layoutY="27.0" prefHeight="48.0" prefWidth="99.0">
                                    <items>
                                        <FXCollections fx:factory="observableArrayList">
                                            <String fx:value="Ascending" />
                                            <String fx:value="Descending" />
                                        </FXCollections>
                                    </items>
                                </ChoiceBox>
                            </children>
                        </Pane>
                        <Pane prefHeight="102.0" prefWidth="397.0">
                            <SplitMenuButton fx:id="splitMenuBtnSearch" layoutX="-1.0" layoutY="27.0" prefHeight="48.0" prefWidth="105.0" text="Search">
                                <items>
                                </items>
                                <font>
                                    <Font size="16.0" />
                                </font>
                            </SplitMenuButton>
                        </Pane>
                    </children>
                </HBox>
                <VBox prefHeight="102.0" prefWidth="161.0">
                    <children>
                        <Pane prefHeight="102.0" prefWidth="17.0">
                            <children>
                                <Button fx:id="orderButton" layoutY="63.0" mnemonicParsing="false" pickOnBounds="true" prefHeight="25.0" prefWidth="111.0" style="-fx-cursor: hand;" text="ORDER">
                                </Button>
                                <Label fx:id="numMediaInCart" layoutY="14.0" prefHeight="17.0" prefWidth="111.0" textFill="#5091e6" />
                                <Button fx:id="cartButton" layoutY="28.0" mnemonicParsing="false" pickOnBounds="true" prefHeight="25.0" prefWidth="111.0" style="-fx-cursor: hand;" text="CART" />
                            </children>
                        </Pane>
                    </children>
                </VBox>
            </children>
        </HBox>
        <VBox layoutY="103.0" prefHeight="678.0" prefWidth="1326.0">
            <children>
                <HBox fx:id="hboxMedia" layoutX="24.0" layoutY="123.0" prefHeight="600.0" prefWidth="1327.0">
                    <children>
                        <VBox fx:id="vboxMedia1" style="-fx-border-color: #33adff;" />
                        <VBox fx:id="vboxMedia2" prefHeight="600.0" prefWidth="330.0" style="-fx-border-color: #33adff;" />
                        <VBox fx:id="vboxMedia3" prefHeight="600.0" prefWidth="330.0" style="-fx-border-color: #33adff;" />
                        <VBox fx:id="vboxMedia4" prefHeight="600.0" prefWidth="320.0" style="-fx-border-color: #33adff;" />
                        <VBox fx:id="vboxMedia5" prefHeight="600.0" prefWidth="331.0" style="-fx-border-color: #33adff;" />
                    </children>
                </HBox>
                <HBox alignment="CENTER" layoutX="24.0" layoutY="723.0" prefHeight="50.0" prefWidth="1327.0" spacing="10.0">
                    <children>
                        <Button fx:id="btnPrevPage" text="Previous" />
                        <Label fx:id="lblPageInfo" text="Page 1 of X" />
                        <Button fx:id="btnNextPage" text="Next" />
                    </children>
                </HBox>
            </children>
        </VBox>
    </children>
</AnchorPane>