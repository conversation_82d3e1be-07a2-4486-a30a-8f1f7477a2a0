<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.text.Font?>

<StackPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="800.0" prefWidth="1400.0" xmlns="http://javafx.com/javafx/20.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.controller.admin.AdminController">
    <children>
        <AnchorPane prefHeight="200.0" prefWidth="200.0">
            <children>
                <BorderPane layoutX="261.0" layoutY="120.0" prefHeight="600.0" prefWidth="1100.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                    <left>
                        <AnchorPane prefHeight="600.0" prefWidth="221.0" BorderPane.alignment="CENTER">
                            <children>
                                <AnchorPane layoutY="-2.0" prefHeight="600.0" prefWidth="221.0" AnchorPane.bottomAnchor="2.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="-2.0">
                                    <children>
                                        <AnchorPane layoutX="10.0" layoutY="2.0" prefHeight="100.0" prefWidth="221.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                            <children>
                                                <Label layoutX="58.0" layoutY="27.0" text="ADMIN">
                                                    <font>
                                                        <Font name="System Bold" size="30.0" />
                                                    </font>
                                                </Label>
                                            </children>
                                        </AnchorPane>
                                        <Label layoutX="14.0" layoutY="108.0" text="Welcome">
                                            <font>
                                                <Font size="15.0" />
                                            </font>
                                        </Label>
                                        <Label fx:id="username" layoutX="84.0" layoutY="105.0" text="ABC">
                                            <font>
                                                <Font size="18.0" />
                                            </font>
                                        </Label>
                                        <Button fx:id="logout_btn" layoutX="8.0" layoutY="736.0" mnemonicParsing="false" onAction="#logout" prefHeight="40.0" prefWidth="200.0" text="Logout" />
                              <Label fx:id="adminEmail" layoutX="25.0" layoutY="138.0" text="Label">
                                 <font>
                                    <Font size="17.0" />
                                 </font>
                              </Label>
                                    </children></AnchorPane>
                            </children>
                        </AnchorPane>
                    </left>
                    <center>
                        <AnchorPane prefHeight="200.0" prefWidth="200.0" BorderPane.alignment="CENTER">
                            <children>
                                <AnchorPane layoutX="58.0" layoutY="135.0" prefHeight="600.0" prefWidth="879.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                    <children>
                                        <AnchorPane fx:id="mainForm" layoutX="9.0" layoutY="9.0" prefHeight="288.0" prefWidth="853.0">
                                            <children>
                                                <TableView fx:id="userTableView" layoutX="10.0" layoutY="6.0" onMouseClicked="#selectUser" prefHeight="314.0" prefWidth="1138.0">
                                                    <columns>
                                                        <TableColumn fx:id="userIDColumn" prefWidth="134.3999729156494" text="ID" />
                                                        <TableColumn fx:id="usernameColumn" prefWidth="373.6000061035156" text="Username" />
                                                        <TableColumn fx:id="passwordColumn" minWidth="2.0" prefWidth="203.20001220703125" text="Password" />
                                                        <TableColumn fx:id="emailColumn" prefWidth="280.00006103515625" text="Email" />
                                                        <TableColumn fx:id="isAdminColumn" prefWidth="148.800048828125" text="is Admin" />
                                                    </columns>
                                                </TableView>
                                            </children></AnchorPane>
                                        <AnchorPane layoutX="28.0" layoutY="372.0" prefHeight="248.0" prefWidth="1122.0">
                                            <children>
                                                <Label layoutX="48.0" layoutY="84.0" text="Username" AnchorPane.leftAnchor="48.0" AnchorPane.topAnchor="84.0">
                                                    <font>
                                                        <Font size="15.0" />
                                                    </font>
                                                </Label>
                                                <TextField fx:id="usernameField" layoutX="186.0" layoutY="82.0" prefHeight="26.0" prefWidth="212.0" AnchorPane.leftAnchor="186.0" AnchorPane.topAnchor="82.0" />
                                                <Label layoutX="50.0" layoutY="145.0" text="Password" AnchorPane.leftAnchor="50.0">
                                                    <font>
                                                        <Font size="15.0" />
                                                    </font>
                                                </Label>
                                    <TextField fx:id="passwordField" layoutX="186.0" layoutY="140.0" prefHeight="26.0" prefWidth="212.0" />
                                    <Label layoutX="619.0" layoutY="83.0" text="Email">
                                       <font>
                                          <Font size="15.0" />
                                       </font>
                                    </Label>
                                    <TextField fx:id="emailField" layoutX="728.0" layoutY="81.0" prefHeight="26.0" prefWidth="212.0" />
                                    <Label layoutX="622.0" layoutY="143.0" text="Role">
                                       <font>
                                          <Font size="15.0" />
                                       </font>
                                    </Label>
                                    <ComboBox fx:id="roleComboBox" editable="true" layoutX="728.0" layoutY="143.0" prefWidth="150.0" />
                                            </children></AnchorPane>
                                        <Button fx:id="userUpdateButton" layoutX="420.0" layoutY="669.0" mnemonicParsing="false" prefHeight="30.0" prefWidth="100.0" text="Update" AnchorPane.bottomAnchor="100.60000000000002" />
                                        <Button fx:id="userDeleteButton" layoutX="709.0" layoutY="669.0" mnemonicParsing="false" prefHeight="30.0" prefWidth="100.0" text="Delete" AnchorPane.bottomAnchor="100.60000000000002" />
                              <Button fx:id="userAddButton" layoutX="266.0" layoutY="669.0" mnemonicParsing="false" prefHeight="30.0" prefWidth="100.0" text="Add" AnchorPane.bottomAnchor="100.60000000000002" />
                              <Button fx:id="clearBtn" layoutX="570.0" layoutY="669.0" mnemonicParsing="false" prefHeight="30.0" prefWidth="100.0" text="Clear" />
                                    </children>
                                </AnchorPane>
                            </children></AnchorPane>
                    </center>
                </BorderPane>
            </children>
        </AnchorPane>
    </children>
</StackPane>
