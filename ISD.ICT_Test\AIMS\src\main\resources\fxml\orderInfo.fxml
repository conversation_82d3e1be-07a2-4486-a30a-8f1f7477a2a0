<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0" xmlns="http://javafx.com/javafx/19" xmlns:fx="http://javafx.com/fxml/1">
	<BorderPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0">
		<top>
			<HBox alignment="CENTER" prefHeight="100.0" prefWidth="200.0" BorderPane.alignment="CENTER">
				<children>
					<ImageView fx:id="aimsImage" fitHeight="103.0" fitWidth="119.0" pickOnBounds="true" preserveRatio="true">
						<Image url="@../assets/images/Logo.png" />
						<HBox.margin>
							<Insets left="30.0" right="30.0" />
						</HBox.margin>
					</ImageView>
					<Separator maxHeight="-Infinity" maxWidth="-Infinity" orientation="VERTICAL" prefHeight="102.0" prefWidth="23.0" />
					<Label contentDisplay="CENTER" prefHeight="86.0" prefWidth="380.0" text="ORDER" textAlignment="CENTER" textFill="#f33061">
						<font>
							<Font name="UTM Eremitage" size="36.0" />
						</font>
						<HBox.margin>
							<Insets left="30.0" />
						</HBox.margin>
					</Label>
					<Pane prefHeight="103.0" prefWidth="733.0" />
					<HBox alignment="CENTER_RIGHT" prefHeight="103.0" prefWidth="335.0">
						<children>
							<!-- ImageView code can be added here if needed -->
						</children>
					</HBox>
				</children>
			</HBox>
		</top>
		<center>
			<VBox prefHeight="219.0" prefWidth="1126.0" style="-fx-background-color: #e1f8ff;" BorderPane.alignment="CENTER">
				<children>
					<HBox prefHeight="40.0" prefWidth="340.0">
						<children>
							<Label prefHeight="36.0" prefWidth="233.0" text="Order ID:" textAlignment="RIGHT">
								<font>
									<Font name="System Bold" size="18.0" />
								</font>
								<padding>
									<Insets left="150.0" />
								</padding>
							</Label>
							<TextField fx:id="searchFieldOrderid" prefHeight="36.0" prefWidth="387.0" />
							<Button fx:id="searchButton" mnemonicParsing="false" prefHeight="79.0" prefWidth="112.0" text="SEARCH">
                        <HBox.margin>
                           <Insets left="30.0" />
                        </HBox.margin>
                        <cursor>
                           <Cursor fx:constant="HAND" />
                        </cursor></Button>
						</children>
                  <VBox.margin>
                     <Insets top="10.0" />
                  </VBox.margin>
					</HBox>
					<HBox prefHeight="40.0" prefWidth="340.0">
						<children>
							<Label prefHeight="37.0" prefWidth="233.0" text="Email:" textAlignment="RIGHT">
								<font>
									<Font name="System Bold" size="18.0" />
								</font>
                        <padding>
                           <Insets left="150.0" />
                        </padding>
							</Label>
							<TextField fx:id="searchFieldEmail" prefHeight="53.0" prefWidth="386.0">
                        <HBox.margin>
                           <Insets />
                        </HBox.margin></TextField>
						</children>
                  <VBox.margin>
                     <Insets top="5.0" />
                  </VBox.margin>
					</HBox>
					<Label fx:id="noOrderFoundLabel" prefHeight="52.0" prefWidth="984.0" text="No orders found for this ID and email" textAlignment="CENTER" textFill="red" visible="false">
						<font>
							<Font name="System Bold" size="24.0" />
						</font>
                  <VBox.margin>
                     <Insets left="150.0" />
                  </VBox.margin>
					</Label>
					<HBox prefHeight="477.0" prefWidth="1326.0">
						<children>
							<GridPane fx:id="gridPane" prefHeight="374.0" prefWidth="650.0">
								<columnConstraints>
									<ColumnConstraints hgrow="SOMETIMES" maxWidth="472.0" minWidth="10.0" prefWidth="287.0" />
									<ColumnConstraints hgrow="SOMETIMES" maxWidth="761.0" minWidth="0.0" prefWidth="343.0" />
								</columnConstraints>
								<rowConstraints>
									<RowConstraints maxHeight="71.0" minHeight="10.0" prefHeight="71.0" />
									<RowConstraints fillHeight="false" maxHeight="40.0" minHeight="40.0" prefHeight="40.0" />
									<RowConstraints fillHeight="false" maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
									<RowConstraints fillHeight="false" maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
									<RowConstraints fillHeight="false" maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
									<RowConstraints fillHeight="false" maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
									<RowConstraints fillHeight="false" maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                           <RowConstraints fillHeight="false" maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
									<RowConstraints fillHeight="false" maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
									<RowConstraints fillHeight="false" maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
									<RowConstraints fillHeight="false" maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
								</rowConstraints>
								<children>
									<Label contentDisplay="CENTER" prefHeight="43.0" prefWidth="142.0" text="Order ID:" textAlignment="CENTER" textFill="#333131" GridPane.rowIndex="1">
										<padding>
											<Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
										</padding>
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label contentDisplay="CENTER" prefHeight="43.0" prefWidth="142.0" text="Name:" textAlignment="CENTER" textFill="#333131" GridPane.rowIndex="2">
										<padding>
											<Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
										</padding>
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label contentDisplay="CENTER" prefHeight="61.0" prefWidth="194.0" text="Phone:" textAlignment="CENTER" textFill="#090909" GridPane.rowIndex="3">
										<padding>
											<Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
										</padding>
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label contentDisplay="CENTER" maxHeight="-Infinity" prefHeight="76.0" prefWidth="169.0" text="Email:" textAlignment="CENTER" textFill="#161616" GridPane.rowIndex="4">
										<padding>
											<Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
										</padding>
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label contentDisplay="CENTER" maxHeight="-Infinity" prefHeight="76.0" prefWidth="169.0" text="Address:" textAlignment="CENTER" textFill="#161616" GridPane.rowIndex="5">
										<padding>
											<Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
										</padding>
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label contentDisplay="CENTER" maxHeight="-Infinity" prefHeight="76.0" prefWidth="169.0" text="Subtotal: (VND)" textAlignment="CENTER" textFill="#161616" GridPane.rowIndex="6">
										<padding>
											<Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
										</padding>
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label contentDisplay="CENTER" maxHeight="-Infinity" prefHeight="76.0" prefWidth="169.0" text="Status:" textAlignment="CENTER" textFill="#161616" GridPane.rowIndex="8">
										<padding>
											<Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
										</padding>
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="labelDelivery" contentDisplay="CENTER" maxHeight="-Infinity" prefHeight="76.0" prefWidth="169.0" text="Delivery Time:" textAlignment="CENTER" textFill="#161616" GridPane.rowIndex="9">
										<padding>
											<Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
										</padding>
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="labelInstruction" contentDisplay="CENTER" maxHeight="-Infinity" prefHeight="76.0" prefWidth="169.0" text="Instruction:" textAlignment="CENTER" textFill="#161616" GridPane.rowIndex="10">
										<padding>
											<Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
										</padding>
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="orderIdField" prefHeight="27.0" prefWidth="374.0" text="123456" textAlignment="CENTER" textFill="#191717" GridPane.columnIndex="1" GridPane.rowIndex="1">
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="recipientNameField" prefHeight="27.0" prefWidth="377.0" text="Alice" textAlignment="CENTER" textFill="#191717" GridPane.columnIndex="1" GridPane.rowIndex="2">
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="phoneField" prefHeight="27.0" prefWidth="372.0" text="0123456789" textAlignment="CENTER" textFill="#100f0f" GridPane.columnIndex="1" GridPane.rowIndex="3">
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="emailField" prefHeight="27.0" prefWidth="372.0" text="<EMAIL>" textAlignment="CENTER" textFill="#100f0f" GridPane.columnIndex="1" GridPane.rowIndex="4">
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="addressField" maxHeight="-Infinity" minHeight="-Infinity" prefHeight="41.0" prefWidth="377.0" text="1 Đại Cồ Việt, Hà Nội " textAlignment="CENTER" textFill="#262424" GridPane.columnIndex="1" GridPane.rowIndex="5">
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="subtotalField" prefHeight="27.0" prefWidth="375.0" text="100.00" textAlignment="CENTER" textFill="#191717" GridPane.columnIndex="1" GridPane.rowIndex="6">
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="statusField" prefHeight="27.0" prefWidth="377.0" text="Pending" textAlignment="CENTER" textFill="#191717" GridPane.columnIndex="1" GridPane.rowIndex="8">
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="deliveryTimeField" prefHeight="27.0" prefWidth="382.0" text="12:00 PM" textAlignment="CENTER" textFill="#191717" GridPane.columnIndex="1" GridPane.rowIndex="9">
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label fx:id="instructionField" prefHeight="27.0" prefWidth="377.0" text="Leave at door" textAlignment="CENTER" textFill="#191717" GridPane.columnIndex="1" GridPane.rowIndex="10">
										<font>
											<Font size="16.0" />
										</font>
									</Label>
									<Label prefHeight="80.0" prefWidth="286.0" text="Order Information:" GridPane.columnSpan="2">
										<font>
											<Font name="System Bold" size="28.0" />
										</font>
									</Label>
                           <Label contentDisplay="CENTER" maxHeight="-Infinity" prefHeight="76.0" prefWidth="169.0" text="Total: (VND)" textAlignment="CENTER" textFill="#161616" GridPane.rowIndex="7">
                              <padding>
                                 <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                              </padding>
                              <font>
                                 <Font size="16.0" />
                              </font>
                           </Label>
                           <Label fx:id="totalField" prefHeight="27.0" prefWidth="375.0" text="100.00" textAlignment="CENTER" textFill="#191717" GridPane.columnIndex="1" GridPane.rowIndex="7">
                              <font>
                                 <Font size="16.0" />
                              </font>
                           </Label>
								</children>
								<padding>
									<Insets left="20.0" />
								</padding>
							</GridPane>
                     <AnchorPane prefHeight="477.0" prefWidth="648.0">
                        <children>
                           <TableView fx:id="tableOrderMedia" layoutX="13.0" layoutY="64.0" prefHeight="357.0" prefWidth="639.0">
                             <columns>
                               <TableColumn fx:id="col_id" prefWidth="40.0" text="ID" />
                               <TableColumn fx:id="col_title" prefWidth="217.0" text="Title" />
                                 <TableColumn fx:id="col_unit_price" prefWidth="140.0" text="Unit Price (VND)" />
                                 <TableColumn fx:id="col_quantity" prefWidth="100.0" text="Quantity" />
                                 <TableColumn fx:id="col_price" prefWidth="140.0" text="Price (VND)" />
                             </columns>
                           </TableView>
                           <Button fx:id="cancelOrderBtn" layoutX="283.0" layoutY="451.0" mnemonicParsing="false" text="Cancel Order">
                              <cursor>
                                 <Cursor fx:constant="HAND" />
                              </cursor>
                           </Button>
                        </children>
                        <HBox.margin>
                           <Insets right="10.0" />
                        </HBox.margin>
                     </AnchorPane>
						</children>
					</HBox>
				</children>
			</VBox>
		</center>
	</BorderPane>
</AnchorPane>
