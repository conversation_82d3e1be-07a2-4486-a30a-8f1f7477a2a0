<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="400.0" prefWidth="600.0" xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1">
    <children>
       <ImageView fx:id="detailedMediaImage" fitHeight="271.0" fitWidth="202.0" layoutX="22.0" layoutY="80.0" pickOnBounds="true" preserveRatio="true">
       </ImageView>
        <Label fx:id="detailedMediaTitle" alignment="CENTER" contentDisplay="CENTER" layoutX="18.0" layoutY="14.0" prefHeight="30.0" prefWidth="564.0" text="Media Title" textAlignment="CENTER">
            <font>
                <Font size="32.0" />
            </font>
        </Label>
      <VBox layoutX="245.0" layoutY="80.0" prefHeight="271.0" prefWidth="338.0" style="-fx-border-color: #80ff80;">
         <children>
            <HBox alignment="CENTER" prefHeight="35.0" prefWidth="337.0">
               <children>
                  <Pane prefHeight="54.0" prefWidth="46.0">
                     <children>
                        <Label alignment="CENTER" layoutX="-33.0" layoutY="10.0" prefHeight="19.0" prefWidth="73.0" text="Price">
                           <font>
                              <Font name="System Bold Italic" size="13.0" />
                           </font>
                        </Label>
                     </children>
                  </Pane>
                  <Pane prefHeight="54.0" prefWidth="201.0">
                     <children>
                        <Label fx:id="detailedMediaPrice" layoutX="31.0" layoutY="10.0" prefHeight="17.0" prefWidth="202.0" text="100" />
                     </children>
                  </Pane>
               </children>
            </HBox>
            <HBox alignment="CENTER" prefHeight="35.0" prefWidth="337.0">
               <children>
                  <Pane prefHeight="54.0" prefWidth="46.0">
                     <children>
                        <Label alignment="CENTER" layoutX="-33.0" layoutY="10.0" prefHeight="19.0" prefWidth="73.0" text="Category">
                           <font>
                              <Font name="System Bold Italic" size="13.0" />
                           </font>
                        </Label>
                     </children>
                  </Pane>
                  <Pane prefHeight="54.0" prefWidth="201.0">
                     <children>
                        <Label fx:id="detailedMediaCategory" layoutX="31.0" layoutY="10.0" prefHeight="17.0" prefWidth="202.0" text="Book" />
                     </children>
                  </Pane>
               </children>
            </HBox>
            <HBox alignment="CENTER" prefHeight="35.0" prefWidth="337.0">
               <children>
                  <Pane prefHeight="54.0" prefWidth="46.0">
                     <children>
                        <Label alignment="CENTER" layoutX="-33.0" layoutY="10.0" prefHeight="19.0" prefWidth="73.0" text="Description">
                           <font>
                              <Font name="System Bold Italic" size="13.0" />
                           </font>
                        </Label>
                     </children>
                  </Pane>
                  <Pane prefHeight="54.0" prefWidth="201.0">
                     <children>
                        <Label fx:id="detailedMediaDescription" layoutX="31.0" layoutY="10.0" prefHeight="17.0" prefWidth="202.0" text="Abc" />
                     </children>
                  </Pane>
               </children>
            </HBox>
            <HBox alignment="CENTER" prefHeight="35.0" prefWidth="337.0">
               <children>
                  <Pane prefHeight="54.0" prefWidth="46.0">
                     <children>
                        <Label alignment="CENTER" layoutX="-33.0" layoutY="10.0" prefHeight="19.0" prefWidth="73.0" text="RushOrder">
                           <font>
                              <Font name="System Bold Italic" size="13.0" />
                           </font>
                        </Label>
                     </children>
                  </Pane>
                  <Pane prefHeight="54.0" prefWidth="201.0">
                     <children>
                        <Label fx:id="detailedMediaRushOrder" layoutX="31.0" layoutY="10.0" prefHeight="17.0" prefWidth="202.0" text="Yes" />
                     </children>
                  </Pane>
               </children>
            </HBox>
            <HBox alignment="CENTER" prefHeight="35.0" prefWidth="337.0">
               <children>
                  <Pane prefHeight="54.0" prefWidth="46.0">
                     <children>
                        <Label fx:id="info1" alignment="CENTER" layoutX="-33.0" layoutY="10.0" prefHeight="19.0" prefWidth="73.0" text="info1">
                           <font>
                              <Font name="System Bold Italic" size="13.0" />
                           </font>
                        </Label>
                     </children>
                  </Pane>
                  <Pane prefHeight="54.0" prefWidth="201.0">
                     <children>
                        <Label fx:id="detailedInfo1" layoutX="31.0" layoutY="10.0" prefHeight="17.0" prefWidth="202.0" text="Abc" />
                     </children>
                  </Pane>
               </children>
            </HBox>
            <HBox alignment="CENTER" prefHeight="35.0" prefWidth="337.0">
               <children>
                  <Pane prefHeight="54.0" prefWidth="46.0">
                     <children>
                        <Label fx:id="info2" alignment="CENTER" layoutX="-33.0" layoutY="10.0" prefHeight="19.0" prefWidth="73.0" text="info2">
                           <font>
                              <Font name="System Bold Italic" size="13.0" />
                           </font>
                        </Label>
                     </children>
                  </Pane>
                  <Pane prefHeight="54.0" prefWidth="201.0">
                     <children>
                        <Label fx:id="detailedInfo2" layoutX="31.0" layoutY="10.0" prefHeight="17.0" prefWidth="202.0" text="Yes" />
                     </children>
                  </Pane>
               </children>
            </HBox>
            <HBox alignment="CENTER" prefHeight="35.0" prefWidth="337.0">
               <children>
                  <Pane prefHeight="54.0" prefWidth="46.0">
                     <children>
                        <Label fx:id="info3" alignment="CENTER" layoutX="-33.0" layoutY="10.0" prefHeight="19.0" prefWidth="73.0" text="info3">
                           <font>
                              <Font name="System Bold Italic" size="13.0" />
                           </font>
                        </Label>
                     </children>
                  </Pane>
                  <Pane prefHeight="54.0" prefWidth="201.0">
                     <children>
                        <Label fx:id="detailedInfo3" layoutX="31.0" layoutY="10.0" prefHeight="17.0" prefWidth="202.0" text="Yes" />
                     </children>
                  </Pane>
               </children>
            </HBox>
            <HBox alignment="CENTER" prefHeight="35.0" prefWidth="337.0">
               <children>
                  <Pane prefHeight="54.0" prefWidth="46.0">
                     <children>
                        <Label fx:id="info4" alignment="CENTER" layoutX="-33.0" layoutY="10.0" prefHeight="19.0" prefWidth="73.0" text="info4">
                           <font>
                              <Font name="System Bold Italic" size="13.0" />
                           </font>
                        </Label>
                     </children>
                  </Pane>
                  <Pane prefHeight="54.0" prefWidth="201.0">
                     <children>
                        <Label fx:id="detailedInfo4" layoutX="31.0" layoutY="10.0" prefHeight="17.0" prefWidth="202.0" text="Yes" />
                     </children>
                  </Pane>
               </children>
            </HBox>
         </children>
      </VBox>
    </children>
</AnchorPane>
