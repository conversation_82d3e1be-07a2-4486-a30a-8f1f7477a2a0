<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane xmlns="http://javafx.com/javafx/19" xmlns:fx="http://javafx.com/fxml/1">
    <BorderPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0" xmlns="http://javafx.com/javafx/19" xmlns:fx="http://javafx.com/fxml/1">
        <top>
            <HBox alignment="CENTER" prefHeight="100.0" prefWidth="200.0" BorderPane.alignment="CENTER">
                <children>
                    <ImageView fx:id="aimsImage" fitHeight="103.0" fitWidth="119.0" pickOnBounds="true" preserveRatio="true">
                        <image>
                            <!--                  <Image url="@AIMS%20Logo.png" />-->
                        </image>
                        <cursor>
                            <Cursor fx:constant="HAND" />
                        </cursor>
                    </ImageView>
                    <Separator maxHeight="-Infinity" maxWidth="-Infinity" orientation="VERTICAL" prefHeight="64.0" prefWidth="34.0" />
                    <Label fx:id="pageTitle" contentDisplay="CENTER" prefHeight="86.0" prefWidth="318.0" text="Invoice" textAlignment="CENTER" textFill="#f33061">
                        <font>
                            <Font name="UTM Eremitage" size="36.0" />
                        </font>
                    </Label>
                    <Pane prefHeight="103.0" prefWidth="733.0" />
                </children>
            </HBox>
        </top>
        <center>
         <HBox spacing="40.0" style="-fx-background-color: e1f8ff;" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" BorderPane.alignment="CENTER">
            <children>
                  <VBox fx:id="coverVBox">
                      <children>
                     <Label prefHeight="80.0" prefWidth="286.0" text="Delivery Information:">
                        <font>
                           <Font size="31.0" />
                        </font>
                     </Label>
                          <GridPane prefWidth="354.0">
                              <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="472.0000305175781" minWidth="10.0" prefWidth="154.99998474121094" />
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="761.3333485921224" minWidth="0.0" prefWidth="761.3333485921224" />
                              </columnConstraints>
                              <rowConstraints>
                                  <RowConstraints maxHeight="-Infinity" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="55.0" minHeight="10.0" prefHeight="55.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="70.33334350585938" minHeight="20.0" prefHeight="37.0" vgrow="SOMETIMES" />
                           <RowConstraints maxHeight="70.33334350585938" minHeight="20.0" prefHeight="37.0" vgrow="SOMETIMES" />
                              </rowConstraints>
                              <children>
                                  <Label contentDisplay="CENTER" prefHeight="43.0" prefWidth="142.0" text="Recipient:" textAlignment="CENTER" textFill="#333131">
                                      <padding>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                      </padding>
                                      <font>
                                          <Font name="SVN-Linux Libertine bold" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label contentDisplay="CENTER" prefHeight="61.0" prefWidth="194.0" text="Phone:" textAlignment="CENTER" textFill="#090909" GridPane.rowIndex="1">
                                      <padding>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                      </padding>
                                      <font>
                                          <Font name="SVN-Linux Libertine bold" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label contentDisplay="CENTER" maxHeight="-Infinity" prefHeight="76.0" prefWidth="169.0" text="Address:" textAlignment="CENTER" textFill="#161616" GridPane.rowIndex="2">
                                      <padding>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                      </padding>
                                      <font>
                                          <Font name="SVN-Linux Libertine bold" size="18.0" />
                                      </font>
                                  </Label>
                           <Label contentDisplay="CENTER" maxHeight="-Infinity" prefHeight="76.0" prefWidth="169.0" text="Email:" textAlignment="CENTER" textFill="#161616" GridPane.rowIndex="3">
                              <padding>
                                 <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                              </padding>
                              <font>
                                 <Font name="SVN-Linux Libertine bold" size="18.0" />
                              </font>
                           </Label>
                                  <Label fx:id="recipientNameField" prefHeight="23.0" prefWidth="180.0" text="Alice" textAlignment="CENTER" textFill="#191717" GridPane.columnIndex="1">
                                      <font>
                                          <Font name="SVN-Linux Libertine" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label fx:id="phoneField" prefHeight="23.0" prefWidth="180.0" text="0123456789" textAlignment="CENTER" textFill="#100f0f" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                      <font>
                                          <Font name="SVN-Linux Libertine" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label fx:id="addressField" maxHeight="-Infinity" minHeight="-Infinity" prefHeight="103.0" prefWidth="593.0" text="1 Đại Cồ Việt, Hà Nội " textAlignment="CENTER" textFill="#262424" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                      <font>
                                          <Font name="SVN-Linux Libertine" size="18.0" />
                                      </font>
                                  </Label>
                           <Label fx:id="emailField" prefHeight="23.0" prefWidth="180.0" text="<EMAIL>" textAlignment="CENTER" textFill="#100f0f" GridPane.columnIndex="1" GridPane.rowIndex="3">
                              <font>
                                 <Font name="SVN-Linux Libertine" size="18.0" />
                              </font>
                           </Label>
                              </children>
                          </GridPane>
                          <HBox alignment="CENTER_LEFT" prefHeight="46.0" style="-fx-background-color: fff; -fx-background-radius: 20;">
                              <children>
                                  <Pane prefHeight="100.0" prefWidth="70.0" />
                                  <Label contentDisplay="CENTER" prefHeight="23.0" prefWidth="317.0" text="Media" textAlignment="CENTER">
                                      <font>
                                          <Font name="SVN-Linux Libertine" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label alignment="CENTER" contentDisplay="CENTER" prefHeight="23.0" prefWidth="190.0" text="Unit price" textAlignment="CENTER">
                                      <font>
                                          <Font name="SVN-Linux Libertine" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label alignment="CENTER" contentDisplay="CENTER" prefHeight="27.0" prefWidth="155.0" text="Quantity" textAlignment="CENTER">
                                      <font>
                                          <Font name="SVN-Linux Libertine" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label alignment="CENTER" contentDisplay="CENTER" prefHeight="23.0" prefWidth="354.0" text="Total" textAlignment="CENTER">
                                      <font>
                                          <Font name="SVN-Linux Libertine" size="18.0" />
                                      </font>
                                  </Label>
                              </children>
                              <VBox.margin>
                                  <Insets top="10.0" />
                              </VBox.margin>
                     </HBox>
                     <ScrollPane prefHeight="200.0" prefWidth="200.0">
                        <content>
                           <VBox fx:id="itemsVBox" prefHeight="200.0" />
                        </content>
                     </ScrollPane>
                      </children>
                  </VBox>
                  <VBox alignment="TOP_CENTER" prefHeight="625.0" prefWidth="507.0" spacing="20.0" style="-fx-background-color: f33061; -fx-background-radius: 40;">
                      <opaqueInsets>
                          <Insets />
                      </opaqueInsets>
                      <children>
                     <Label text="Payment information" textFill="WHITE">
                        <padding>
                           <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                        </padding>
                        <font>
                           <Font size="25.0" />
                        </font>
                     </Label>
                          <Separator maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308">
                              <opaqueInsets>
                                  <Insets />
                              </opaqueInsets>
                              <VBox.margin>
                                  <Insets top="-10.0" />
                              </VBox.margin>
                          </Separator>
                          <GridPane prefWidth="354.0">
                              <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="172.60009765625" minWidth="10.0" prefWidth="152.0" />
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="206.93334960937497" minWidth="10.0" prefWidth="151.0" />
                              </columnConstraints>
                              <rowConstraints>
                                  <RowConstraints maxHeight="-Infinity" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="-Infinity" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="-Infinity" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                              </rowConstraints>
                              <children>
                                  <Label contentDisplay="CENTER" prefHeight="43.0" prefWidth="155.0" text="Subtotal:" textAlignment="CENTER" textFill="WHITE">
                                      <padding>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                      </padding>
                                      <font>
                                          <Font name="SVN-Linux Libertine bold" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label contentDisplay="CENTER" prefHeight="61.0" prefWidth="194.0" text="VAT (10%):" textAlignment="CENTER" textFill="WHITE" GridPane.rowIndex="1">
                                      <padding>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                      </padding>
                                      <font>
                                          <Font name="SVN-Linux Libertine bold" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label fx:id="subtotalLabel" prefHeight="23.0" prefWidth="180.0" text="80.000 VNĐ " textAlignment="CENTER" textFill="WHITE" GridPane.columnIndex="1">
                                      <font>
                                          <Font name="SVN-Linux Libertine" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label fx:id="vatLabel" prefHeight="23.0" prefWidth="180.0" text="6.400 VNĐ " textAlignment="CENTER" textFill="WHITE" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                      <font>
                                          <Font name="SVN-Linux Libertine" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label contentDisplay="CENTER" prefHeight="61.0" prefWidth="194.0" text="Shipping fee:" textAlignment="CENTER" textFill="WHITE" GridPane.rowIndex="2">
                                      <padding>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                      </padding>
                                      <font>
                                          <Font name="SVN-Linux Libertine bold" size="18.0" />
                                      </font>
                                  </Label>
                                  <Label fx:id="shippingFeeLabel" prefHeight="23.0" prefWidth="180.0" text="25.000 VNĐ " textAlignment="CENTER" textFill="WHITE" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                      <font>
                                          <Font name="SVN-Linux Libertine" size="18.0" />
                                      </font>
                                  </Label>
                              </children>
                              <VBox.margin>
                                  <Insets />
                              </VBox.margin>
                          </GridPane>
                          <Separator maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308">
                              <opaqueInsets>
                                  <Insets />
                              </opaqueInsets>
                              <VBox.margin>
                                  <Insets />
                              </VBox.margin>
                          </Separator>
                          <GridPane prefWidth="354.0">
                              <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="172.60009765625" minWidth="10.0" prefWidth="150.933447265625" />
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="206.93334960937497" minWidth="10.0" prefWidth="199.46655273437497" />
                              </columnConstraints>
                              <rowConstraints>
                                  <RowConstraints maxHeight="-Infinity" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                              </rowConstraints>
                              <children>
                                  <Label contentDisplay="CENTER" prefHeight="43.0" prefWidth="155.0" text="Price:" textAlignment="CENTER" textFill="WHITE">
                                      <padding>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                      </padding>
                                      <font>
                                          <Font name="SVN-Linux Libertine bold" size="22.0" />
                                      </font>
                                  </Label>
                                  <Label fx:id="priceLabel" prefHeight="23.0" prefWidth="180.0" text="111.400 VNĐ " textAlignment="CENTER" textFill="#ffd622" GridPane.columnIndex="1">
                                      <font>
                                          <Font name="SVN-Linux Libertine bold" size="22.0" />
                                      </font>
                                  </Label>
                              </children>
                          </GridPane>
                            <Button fx:id="payOrderButton" contentDisplay="CENTER" mnemonicParsing="false" prefHeight="50.0" prefWidth="200.0" style="-fx-background-color: #ffd622; -fx-background-radius: 10;" text="Pay order" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                <font>
                                    <Font size="18.0" />
                                </font>
                                <cursor>
                                    <Cursor fx:constant="HAND" />
                                </cursor>
                        <VBox.margin>
                           <Insets top="50.0" />
                        </VBox.margin>
                            </Button>
                     <Button fx:id="cancelOrderButton" contentDisplay="CENTER" mnemonicParsing="false" prefHeight="50.0" prefWidth="200.0" style="-fx-background-color: white; -fx-background-radius: 10;" text="Cancel order">
                        <font>
                           <Font size="18.0" />
                        </font>
                         <cursor>
                             <Cursor fx:constant="HAND" />
                         </cursor>
                     </Button>
                      </children>
                  <padding>
                     <Insets left="20.0" right="20.0" top="10.0" />
                  </padding>
                  </VBox>
            </children>
            <padding>
               <Insets bottom="40.0" left="40.0" right="40.0" top="20.0" />
            </padding>
         </HBox>
        </center>
    </BorderPane>
   <AnchorPane fx:id="loadingOverlay" prefHeight="200.0" prefWidth="200.0" style="-fx-background-color: rgba(0,0,0,0.3);" visible="false" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
      <children>
         <ProgressIndicator fx:id="loadingProgress" minHeight="100.0" minWidth="100.0" AnchorPane.bottomAnchor="200.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="200.0" />
      </children>
   </AnchorPane>
</AnchorPane>
