<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.hust.ict</groupId>
	<artifactId>AIMS</artifactId>
	<version>1.0-SNAPSHOT</version>
	<name>AIMS</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<junit.version>5.10.0</junit.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.openjfx</groupId>
			<artifactId>javafx-controls</artifactId>
			<version>19.0.2</version>
		</dependency>
		<dependency>
			<groupId>org.openjfx</groupId>
			<artifactId>javafx-fxml</artifactId>
			<version>19.0.2</version>
		</dependency>
		<dependency>
			<groupId>org.controlsfx</groupId>
			<artifactId>controlsfx</artifactId>
			<version>11.1.2</version>
		</dependency>
		<dependency>
			<groupId>com.dlsc.formsfx</groupId>
			<artifactId>formsfx-core</artifactId>
			<version>11.6.0</version>
			<exclusions>
				<exclusion>
					<groupId>org.openjfx</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.kordamp.bootstrapfx</groupId>
			<artifactId>bootstrapfx-core</artifactId>
			<version>0.4.0</version>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-runner</artifactId>
			<version>1.2.0</version>
			<scope>test</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.postgresql/postgresql -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.7.7</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.oracle.database.jdbc/ojdbc10 -->
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc10</artifactId>
			<version>19.21.0.0</version>
		</dependency>


		<!--
		https://mvnrepository.com/artifact/jakarta.platform/jakarta.jakartaee-api -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.14</version>
		</dependency>
		
		<!-- Small web server for parsing URL response from VNPay -->		
		<!-- https://mvnrepository.com/artifact/net.freeutils/jlhttp -->
		<dependency>
		    <groupId>net.freeutils</groupId>
		    <artifactId>jlhttp</artifactId>
		    <version>3.1</version>
		</dependency>

		<!-- Testing -->
		<dependency>
		    <groupId>org.mockito</groupId>
		    <artifactId>mockito-core</artifactId>
		    <version>5.12.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.unitils/unitils-core -->
		<dependency>
		    <groupId>org.unitils</groupId>
		    <artifactId>unitils-core</artifactId>
		    <version>3.4.6</version>
		    <scope>test</scope>
		</dependency>

		<!-- Mailing service -->
		<dependency>
			<groupId>org.simplejavamail</groupId>
			<artifactId>simple-java-mail</artifactId>
			<version>8.11.2</version>
		</dependency>

		<dependency>
			<groupId>com.google.api-client</groupId>
			<artifactId>google-api-client</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.oauth-client</groupId>
			<artifactId>google-oauth-client-jetty</artifactId>
			<version>1.34.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-gmail</artifactId>
			<version>v1-rev20220404-2.0.0</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<!-- <version>3.11.0</version> -->
				<configuration>
					<source>17</source>
					<target>17</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<!-- <version>2.22.0</version> -->
				<dependencies>
					<dependency>
						<groupId>org.junit.platform</groupId>
						<artifactId>junit-platform-surefire-provider</artifactId>
						<version>1.2.0</version>
					</dependency>
				</dependencies>
				<configuration>
					<additionalClasspathElements>
						<additionalClasspathElement>src/test/java/</additionalClasspathElement>
					</additionalClasspathElements>
				</configuration>
			</plugin>


			<plugin>
				<groupId>org.openjfx</groupId>
				<artifactId>javafx-maven-plugin</artifactId>
				<version>0.0.8</version>
				<executions>
					<execution>
						<!-- Default configuration for running with: mvn clean
						javafx:run -->
						<id>default-cli</id>
						<configuration>
							<mainClass>com.hust.ict.aims/com.hust.ict.aims.Main</mainClass>
							<launcher>app</launcher>
							<jlinkZipName>app</jlinkZipName>
							<jlinkImageName>app</jlinkImageName>
							<noManPages>true</noManPages>
							<stripDebug>true</stripDebug>
							<noHeaderFiles>true</noHeaderFiles>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>