<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="788.0" prefWidth="1326.0" style="-fx-border-radius: 50px;" xmlns="http://javafx.com/javafx/20.0.1" xmlns:fx="http://javafx.com/fxml/1">
    <HBox prefHeight="80.0" prefWidth="1326.0">
        <children>
            <ImageView fx:id="aimsImage" fitHeight="80.0" fitWidth="105.0" pickOnBounds="true" preserveRatio="true">
                <image>
                    <Image url="@../assets/images/Logo.png" />
                </image>
                <cursor>
                    <Cursor fx:constant="HAND" />
                </cursor>
            </ImageView>
            <Separator orientation="VERTICAL" prefHeight="80.0" prefWidth="56.0" />
            <Label prefHeight="80.0" prefWidth="533.0" text="Rush Delivery Information Form" textFill="#f33061">
                <font>
                    <Font size="36.0" />
                </font>
            </Label>
        </children>
        <padding>
            <Insets left="40.0" />
        </padding>
    </HBox>
    <GridPane hgap="10" layoutX="182.0" layoutY="131.0" prefHeight="453.0" prefWidth="944.0" vgap="10">
      <ChoiceBox fx:id="dayChoiceBox" prefWidth="67.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
         <GridPane.margin>
            <Insets left="60.0" />
         </GridPane.margin>
      </ChoiceBox>

        <Label prefHeight="27.0" prefWidth="207.0" text="Desired delivery time: *" GridPane.rowIndex="2">
            <font>
                <Font name="System Bold" size="18.0" />
            </font></Label>

        <Label alignment="TOP_LEFT" text="Instruction:" GridPane.rowIndex="6">
            <font>
                <Font name="System Bold" size="18.0" />
            </font></Label>
        <TextField fx:id="instructionsField" alignment="TOP_LEFT" prefHeight="323.0" prefWidth="845.0" GridPane.columnIndex="1" GridPane.rowIndex="6">
            <font>
                <Font size="16.0" />
            </font>
        </TextField>
      <Label prefHeight="27.0" prefWidth="56.0" text="Date:" GridPane.columnIndex="1" GridPane.rowIndex="2">
         <font>
            <Font name="System Bold" size="18.0" />
         </font>
      </Label>
      <Label prefHeight="27.0" prefWidth="67.0" text="Time:" GridPane.columnIndex="1" GridPane.rowIndex="2">
         <font>
            <Font name="System Bold" size="18.0" />
         </font>
         <GridPane.margin>
            <Insets left="370.0" />
         </GridPane.margin>
      </Label>
      <ChoiceBox fx:id="monthChoiceBox" prefWidth="67.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
         <GridPane.margin>
            <Insets left="140.0" />
         </GridPane.margin>
      </ChoiceBox>
      <ChoiceBox fx:id="yearChoiceBox" prefWidth="67.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
         <GridPane.margin>
            <Insets left="220.0" />
         </GridPane.margin>
      </ChoiceBox>
      <ChoiceBox fx:id="hourChoiceBox" prefWidth="67.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
         <GridPane.margin>
            <Insets left="420.0" />
         </GridPane.margin>
      </ChoiceBox>
      <ChoiceBox fx:id="minuteChoiceBox" prefWidth="67.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
         <GridPane.margin>
            <Insets left="500.0" />
         </GridPane.margin>
      </ChoiceBox>
        <columnConstraints>
            <ColumnConstraints maxWidth="344.59998779296876" minWidth="94.0" prefWidth="218.40000610351564" />
            <ColumnConstraints maxWidth="840.0" minWidth="588.6000122070312" prefWidth="715.1999938964843" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints />
            <RowConstraints />
            <RowConstraints />
            <RowConstraints />
            <RowConstraints />
         <RowConstraints />
         <RowConstraints />
        </rowConstraints>
    </GridPane>

    <VBox layoutX="25.0" layoutY="91.0" maxHeight="300.0" maxWidth="300.0" prefWidth="150.0" spacing="10">
        <VBox id="cartItems" spacing="10">
            <VBox />
        </VBox>
    </VBox>
    <Button fx:id="cancelRushDeliveryButton" layoutX="416.0" layoutY="654.0" mnemonicParsing="false" prefHeight="53.0" prefWidth="166.0" style="-fx-background-color: red; -fx-border-radius: 50;" text="Cancel" textFill="#fdfafa">
        <font>
            <Font name="System Bold" size="16.0" />
        </font>
        <cursor>
            <Cursor fx:constant="HAND" />
        </cursor>
    </Button>
    <Button fx:id="proceedRushDeliveryButton" layoutX="817.0" layoutY="654.0" mnemonicParsing="false" prefHeight="53.0" prefWidth="166.0" style="-fx-background-color: green; -fx-border-radius: 50;" text="Submit" textFill="#fdfafa">
        <font>
            <Font name="System Bold" size="16.0" />
        </font>
        <cursor>
            <Cursor fx:constant="HAND" />
        </cursor>
    </Button>
   <Label layoutX="247.0" layoutY="585.0" text="*The fields that are marked with an asterisk are mandatory to fill in">
      <font>
         <Font name="System Bold Italic" size="14.0" />
      </font>
   </Label>
</AnchorPane>
