<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.text.Font?>

<StackPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="800.0" prefWidth="1400.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.controller.productmanager.ProductManagerController">
   <children>
      <AnchorPane fx:id="main_form" prefHeight="200.0" prefWidth="200.0">
         <children>
            <BorderPane layoutX="261.0" layoutY="120.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <left>
                  <AnchorPane prefHeight="600.0" prefWidth="221.0" BorderPane.alignment="CENTER">
                     <children>
                        <AnchorPane layoutY="-2.0" prefHeight="600.0" prefWidth="221.0" AnchorPane.bottomAnchor="2.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="-2.0">
                           <children>
                              <AnchorPane layoutX="10.0" layoutY="2.0" prefHeight="100.0" prefWidth="221.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                 <children>
                                    <Label layoutX="75.0" layoutY="32.0" text="AIMS">
                                       <font>
                                          <Font name="System Bold" size="30.0" />
                                       </font>
                                    </Label>
                                 </children>
                              </AnchorPane>
                              <Label layoutX="14.0" layoutY="107.0" text="Welcome">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label fx:id="username" layoutX="100.0" layoutY="105.0" text="ABC">
                                 <font>
                                    <Font size="18.0" />
                                 </font>
                              </Label>
                              <Button layoutX="9.0" layoutY="170.0" mnemonicParsing="false" prefHeight="40.0" prefWidth="200.0" text="DASHBOARD" />
                              <Button fx:id="medias_btn" layoutX="8.0" layoutY="232.0" mnemonicParsing="false" onMouseClicked="#manageMedias" prefHeight="40.0" prefWidth="200.0" text="MEDIAS" />
                              <Button layoutX="8.0" layoutY="292.0" mnemonicParsing="false" onMouseClicked="#manageOrders" prefHeight="40.0" prefWidth="200.0" text="ORDERS" />
                              <Button fx:id="logout_btn" layoutX="8.0" layoutY="736.0" mnemonicParsing="false" onAction="#logout" prefHeight="40.0" prefWidth="200.0" text="Logout" />
                              <Label fx:id="productManagerEmail" layoutX="14.0" layoutY="132.0" prefHeight="28.0" prefWidth="188.0" text="ABC" />
                              <Button fx:id="changepassword_btn" layoutX="8.0" layoutY="353.0" mnemonicParsing="false" onAction="#changePasswordBtn" prefHeight="40.0" prefWidth="200.0" text="CHANGE PASSWORD" />
                           </children></AnchorPane>
                     </children>
                  </AnchorPane>
               </left>
               <center>
                  <AnchorPane fx:id="displayPane" prefHeight="200.0" prefWidth="200.0" BorderPane.alignment="CENTER" />
               </center>
            </BorderPane>
         </children>
      </AnchorPane>
   </children>
</StackPane>
