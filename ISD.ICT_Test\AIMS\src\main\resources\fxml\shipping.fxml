<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="788.0" prefWidth="1326.0" style="-fx-border-radius: 50px;" xmlns="http://javafx.com/javafx/20.0.1" xmlns:fx="http://javafx.com/fxml/1">
   <HBox prefHeight="80.0" prefWidth="1326.0">
      <children>
         <ImageView fx:id="aimsImage" fitHeight="80.0" fitWidth="105.0" pickOnBounds="true" preserveRatio="true">
            <image>
               <Image url="@../assets/images/Logo.png" />
            </image>
            <cursor>
               <Cursor fx:constant="HAND" />
            </cursor>
         </ImageView>
         <Separator orientation="VERTICAL" prefHeight="80.0" prefWidth="56.0" />
         <Label prefHeight="80.0" prefWidth="416.0" text="Delivery Information Form" textFill="#f33061">
            <font>
               <Font size="36.0" />
            </font>
         </Label>
      </children>
      <padding>
         <Insets left="40.0" />
      </padding>
   </HBox>
    <GridPane hgap="10" layoutX="191.0" layoutY="114.0" prefHeight="453.0" prefWidth="944.0" vgap="10">
        <!--                <ColumnConstraints percentWidth="50"/>-->
        <!--                <ColumnConstraints percentWidth="50"/>-->

        <Label text="Name: *" GridPane.columnIndex="0" GridPane.rowIndex="0">
         <font>
            <Font name="System Bold" size="18.0" />
         </font></Label>
        <TextField fx:id="nameField" GridPane.columnIndex="1" GridPane.rowIndex="0">
         <font>
            <Font size="16.0" />
         </font></TextField>

        <Label text="Phone: *" GridPane.columnIndex="0" GridPane.rowIndex="1">
         <font>
            <Font name="System Bold" size="18.0" />
         </font></Label>
        <TextField fx:id="phoneField" GridPane.columnIndex="1" GridPane.rowIndex="1">
         <font>
            <Font size="16.0" />
         </font></TextField>

        <Label text="Province: *" GridPane.rowIndex="2">
         <font>
            <Font name="System Bold" size="18.0" />
         </font></Label>
      <Label text="Email: *" GridPane.rowIndex="4">
         <font>
            <Font name="System Bold" size="18.0" />
         </font>
      </Label>
      <ChoiceBox fx:id="provinceField" prefHeight="33.0" prefWidth="331.0" style="-fx-font-size: 16px;" GridPane.columnIndex="1" GridPane.rowIndex="2">
         <cursor>
            <Cursor fx:constant="HAND" />
         </cursor></ChoiceBox>

        <Label text="Address: *" GridPane.rowIndex="3">
         <font>
            <Font name="System Bold" size="18.0" />
         </font></Label>
      <TextField fx:id="emailField" prefHeight="43.0" prefWidth="806.0" GridPane.columnIndex="1" GridPane.rowIndex="4">
         <font>
            <Font size="16.0" />
         </font></TextField>
        <TextField fx:id="addressField" GridPane.columnIndex="1" GridPane.rowIndex="3">
         <font>
            <Font size="16.0" />
         </font></TextField>

        <Label alignment="TOP_LEFT" text="Instruction:" GridPane.rowIndex="5">
         <font>
            <Font name="System Bold" size="18.0" />
         </font></Label>
        <TextField fx:id="instructionsField" alignment="TOP_LEFT" prefHeight="323.0" prefWidth="845.0" GridPane.columnIndex="1" GridPane.rowIndex="5">
         <font>
            <Font size="16.0" />
         </font>
      </TextField>
        <columnConstraints>
            <ColumnConstraints maxWidth="128.0" minWidth="94.0" prefWidth="128.0" />
            <ColumnConstraints maxWidth="840.0" minWidth="806.0" prefWidth="806.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints />
            <RowConstraints />
            <RowConstraints />
            <RowConstraints />
            <RowConstraints maxHeight="52.399993896484375" minHeight="32.800006103515614" prefHeight="32.80000610351564" />
         <RowConstraints maxHeight="222.59999389648436" minHeight="201.60000610351562" prefHeight="222.39999389648438" />
        </rowConstraints>
    </GridPane>

    <VBox layoutX="25.0" layoutY="91.0" maxHeight="300.0" maxWidth="300.0" prefWidth="150.0" spacing="10">
        <VBox id="cartItems" spacing="10">
            <VBox />
        </VBox>
    </VBox>
    <Button fx:id="submitDeliveryInfoButton" layoutX="415.0" layoutY="673.0" mnemonicParsing="false" prefHeight="53.0" prefWidth="166.0" style="-fx-background-color: red; -fx-border-radius: 50;" text="Submit" textFill="#fdfafa">
        <font>
            <Font name="System Bold" size="16.0" />
        </font>
      <cursor>
         <Cursor fx:constant="HAND" />
      </cursor>
    </Button>
   <Button fx:id="placeRushOrderButton" layoutX="813.0" layoutY="673.0" mnemonicParsing="false" prefHeight="53.0" prefWidth="166.0" style="-fx-background-color: green; -fx-border-radius: 50;" text="Place rush order" textFill="#fdfafa">
      <font>
         <Font name="System Bold" size="16.0" />
      </font>
      <cursor>
         <Cursor fx:constant="HAND" />
      </cursor>
   </Button>
   <Label layoutX="249.0" layoutY="599.0" text="*The fields that are marked with an asterisk are mandatory to fill in">
      <font>
         <Font name="System Bold Italic" size="14.0" />
      </font>
   </Label>
</AnchorPane>
