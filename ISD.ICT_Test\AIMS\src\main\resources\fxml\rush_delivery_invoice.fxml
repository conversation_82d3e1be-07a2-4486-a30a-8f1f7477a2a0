<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1">
    <BorderPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0" xmlns="http://javafx.com/javafx/19" xmlns:fx="http://javafx.com/fxml/1">
        <top>
            <HBox alignment="CENTER" prefHeight="100.0" prefWidth="200.0" BorderPane.alignment="CENTER">
                <children>
                    <ImageView fx:id="aimsImage" fitHeight="103.0" fitWidth="119.0" pickOnBounds="true" preserveRatio="true">
                        <image>
                            <!--                  <Image url="@AIMS%20Logo.png" />-->
                        </image>
                        <cursor>
                            <Cursor fx:constant="HAND" />
                        </cursor>
                    </ImageView>
                    <Separator maxHeight="-Infinity" maxWidth="-Infinity" orientation="VERTICAL" prefHeight="64.0" prefWidth="34.0" />
                    <Label fx:id="pageTitle" contentDisplay="CENTER" prefHeight="86.0" prefWidth="350.0" text="Rush Delivery Invoice" textAlignment="CENTER" textFill="#f33061">
                        <font>
                            <Font name="UTM Eremitage" size="36.0" />
                        </font>
                    </Label>
                    <Pane prefHeight="103.0" prefWidth="733.0" />
                </children>
            </HBox>
        </top>
        <center>
            <VBox fx:id="coverVBox" prefHeight="635.0" prefWidth="959.0" style="-fx-background-color: e1f8ff;" BorderPane.alignment="CENTER">
                <children>
                    <Label prefHeight="80.0" prefWidth="298.0" text="Delivery Information:">
                        <font>
                            <Font size="31.0" />
                        </font>
                    </Label>
                    <GridPane prefWidth="354.0">
                        <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="472.0000305175781" minWidth="10.0" prefWidth="154.99998474121094" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="761.3333485921224" minWidth="0.0" prefWidth="761.3333485921224" />
                        </columnConstraints>
                        <rowConstraints>
                            <RowConstraints maxHeight="-Infinity" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="55.0" minHeight="10.0" prefHeight="55.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="70.33334350585938" minHeight="20.0" prefHeight="37.0" vgrow="SOMETIMES" />
                     <RowConstraints maxHeight="70.33334350585938" minHeight="20.0" prefHeight="37.0" vgrow="SOMETIMES" />
                        </rowConstraints>
                        <children>
                            <Label contentDisplay="CENTER" prefHeight="43.0" prefWidth="142.0" text="Recipient:" textAlignment="CENTER" textFill="#333131">
                                <padding>
                                    <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                </padding>
                                <font>
                                    <Font name="SVN-Linux Libertine bold" size="18.0" />
                                </font>
                            </Label>
                            <Label contentDisplay="CENTER" prefHeight="61.0" prefWidth="194.0" text="Phone:" textAlignment="CENTER" textFill="#090909" GridPane.rowIndex="1">
                                <padding>
                                    <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                </padding>
                                <font>
                                    <Font name="SVN-Linux Libertine bold" size="18.0" />
                                </font>
                            </Label>
                            <Label contentDisplay="CENTER" maxHeight="-Infinity" prefHeight="76.0" prefWidth="169.0" text="Address:" textAlignment="CENTER" textFill="#161616" GridPane.rowIndex="2">
                                <padding>
                                    <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                </padding>
                                <font>
                                    <Font name="SVN-Linux Libertine bold" size="18.0" />
                                </font>
                            </Label>
                            <Label fx:id="recipientNameField" prefHeight="27.0" prefWidth="628.0" text="Alice" textAlignment="CENTER" textFill="#191717" GridPane.columnIndex="1">
                                <font>
                                    <Font name="SVN-Linux Libertine" size="18.0" />
                                </font>
                            </Label>
                            <Label fx:id="phoneField" prefHeight="23.0" prefWidth="180.0" text="0123456789" textAlignment="CENTER" textFill="#100f0f" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <font>
                                    <Font name="SVN-Linux Libertine" size="18.0" />
                                </font>
                            </Label>
                            <Label fx:id="addressField" maxHeight="-Infinity" minHeight="-Infinity" prefHeight="103.0" prefWidth="690.0" text="1 Đại Cồ Việt, Hà Nội " textAlignment="CENTER" textFill="#262424" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                <font>
                                    <Font name="SVN-Linux Libertine" size="18.0" />
                                </font>
                            </Label>
                     <Label contentDisplay="CENTER" text="Email:" textAlignment="CENTER" GridPane.rowIndex="3">
                        <font>
                           <Font size="18.0" />
                        </font>
                        <padding>
                           <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                        </padding>
                     </Label>
                     <Label fx:id="emailField" prefHeight="27.0" prefWidth="602.0" text="Label" textAlignment="CENTER" GridPane.columnIndex="1" GridPane.rowIndex="3">
                        <font>
                           <Font size="18.0" />
                        </font>
                     </Label>
                        </children>
                    </GridPane>
                </children>
            </VBox>
        </center>
        <left>
            <VBox prefHeight="635.0" prefWidth="40.0" style="-fx-background-color: e1f8ff;" BorderPane.alignment="CENTER" />
        </left>
        <bottom>
            <Pane prefHeight="50.0" prefWidth="1366.0" style="-fx-background-color: e1f8ff;" BorderPane.alignment="CENTER" />
        </bottom>
        <right>
            <VBox fx:id="paymentInfoVBox" alignment="TOP_CENTER" prefHeight="614.0" prefWidth="349.0" style="-fx-background-color: f33061; -fx-background-radius: 40;" BorderPane.alignment="CENTER">
                <opaqueInsets>
                    <Insets />
                </opaqueInsets>
                <children>
                    <Label text="Payment information" textFill="WHITE">
                        <padding>
                            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                        </padding>
                        <font>
                            <Font size="28.0" />
                        </font>
                    </Label>
                    <Separator maxHeight="-Infinity" maxWidth="-Infinity" prefHeight="0.0" prefWidth="251.0">
                        <opaqueInsets>
                            <Insets />
                        </opaqueInsets>
                        <VBox.margin>
                            <Insets bottom="20.0" />
                        </VBox.margin>
                    </Separator>
               <VBox fx:id="rushDeliveryVBox" prefWidth="100.0">
                  <children>
                     <Label text="Rush Delivery " textFill="WHITE">
                        <font>
                           <Font size="24.0" />
                        </font>
                        <VBox.margin>
                           <Insets left="100.0" />
                        </VBox.margin>
                     </Label>
                     <GridPane>
                       <columnConstraints>
                         <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                         <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                       </columnConstraints>
                       <rowConstraints>
                         <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                         <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                         <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                       </rowConstraints>
                        <VBox.margin>
                           <Insets top="5.0" />
                        </VBox.margin>
                        <children>
                           <Label text="VAT(10%):" textFill="WHITE" GridPane.rowIndex="1">
                              <font>
                                 <Font size="18.0" />
                              </font>
                              <GridPane.margin>
                                 <Insets left="20.0" />
                              </GridPane.margin>
                           </Label>
                           <Label fx:id="rushDeliveryShipFeeLabel" text="Label" textFill="WHITE" GridPane.columnIndex="1" GridPane.rowIndex="2">
                              <font>
                                 <Font size="18.0" />
                              </font>
                           </Label>
                           <Label text="Shipping fee:" textFill="WHITE" GridPane.rowIndex="2">
                              <font>
                                 <Font size="18.0" />
                              </font>
                              <GridPane.margin>
                                 <Insets left="20.0" />
                              </GridPane.margin>
                           </Label>
                           <Label fx:id="rushDeliveryVatLabel" text="Label" textFill="WHITE" GridPane.columnIndex="1" GridPane.rowIndex="1">
                              <font>
                                 <Font size="18.0" />
                              </font>
                           </Label>
                           <Label text="Subtotal:" textFill="WHITE">
                              <font>
                                 <Font size="18.0" />
                              </font>
                              <GridPane.margin>
                                 <Insets left="20.0" />
                              </GridPane.margin>
                           </Label>
                           <Label fx:id="rushDeliverySubtotalLabel" text="Label" textFill="WHITE" GridPane.columnIndex="1">
                              <font>
                                 <Font size="18.0" />
                              </font>
                           </Label>
                        </children>
                     </GridPane>
                     <Separator maxHeight="-Infinity" maxWidth="-Infinity" prefHeight="0.0" prefWidth="260.0">
                        <VBox.margin>
                           <Insets bottom="20.0" left="45.0" top="20.0" />
                        </VBox.margin>
                     </Separator>
                  </children>
               </VBox>
               <VBox fx:id="regularDeliveryVBox" prefWidth="100.0">
                  <children>
                     <Label text="Regular Delivery " textFill="WHITE">
                        <font>
                           <Font size="24.0" />
                        </font>
                        <VBox.margin>
                           <Insets left="100.0" />
                        </VBox.margin>
                     </Label>
                     <GridPane>
                        <columnConstraints>
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                        </columnConstraints>
                        <rowConstraints>
                           <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                           <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                           <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        </rowConstraints>
                        <VBox.margin>
                           <Insets top="5.0" />
                        </VBox.margin>
                        <children>
                           <Label text="VAT(10%):" textFill="WHITE" GridPane.rowIndex="1">
                              <font>
                                 <Font size="18.0" />
                              </font>
                              <GridPane.margin>
                                 <Insets left="20.0" />
                              </GridPane.margin>
                           </Label>
                           <Label fx:id="regularDeliveryShipFeeLabel" text="Label" textFill="WHITE" GridPane.columnIndex="1" GridPane.rowIndex="2">
                              <font>
                                 <Font size="18.0" />
                              </font>
                           </Label>
                           <Label text="Shipping fee:" textFill="WHITE" GridPane.rowIndex="2">
                              <font>
                                 <Font size="18.0" />
                              </font>
                              <GridPane.margin>
                                 <Insets left="20.0" />
                              </GridPane.margin>
                           </Label>
                           <Label fx:id="regularDeliveryVatLabel" text="Label" textFill="WHITE" GridPane.columnIndex="1" GridPane.rowIndex="1">
                              <font>
                                 <Font size="18.0" />
                              </font>
                           </Label>
                           <Label text="Subtotal:" textFill="WHITE">
                              <font>
                                 <Font size="18.0" />
                              </font>
                              <GridPane.margin>
                                 <Insets left="20.0" />
                              </GridPane.margin>
                           </Label>
                           <Label fx:id="regularDeliverySubtotalLabel" text="Label" textFill="WHITE" GridPane.columnIndex="1">
                              <font>
                                 <Font size="18.0" />
                              </font>
                           </Label>
                        </children>
                     </GridPane>
                     <Separator maxHeight="-Infinity" maxWidth="-Infinity" prefHeight="0.0" prefWidth="260.0">
                        <VBox.margin>
                           <Insets bottom="20.0" left="45.0" top="20.0" />
                        </VBox.margin>
                     </Separator>
                  </children>
               </VBox>
               <GridPane>
                 <columnConstraints>
                   <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                   <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                 </columnConstraints>
                 <rowConstraints>
                   <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                 </rowConstraints>
                  <children>
                     <Label text="Price:" textFill="WHITE">
                        <font>
                           <Font size="20.0" />
                        </font>
                        <GridPane.margin>
                           <Insets left="20.0" />
                        </GridPane.margin>
                     </Label>
                     <Label fx:id="priceLabel" text="Label" textFill="#ffd622" GridPane.columnIndex="1">
                        <font>
                           <Font size="20.0" />
                        </font>
                     </Label>
                  </children>
               </GridPane>
               <Pane prefHeight="140.0" prefWidth="200.0">
                  <children>
                     <Button fx:id="payOrderBtn" contentDisplay="CENTER" layoutX="69.0" mnemonicParsing="false" prefHeight="56.0" prefWidth="211.0" style="-fx-background-color: #ffd622; -fx-background-radius: 10;" text="Pay order">
                        <font>
                           <Font size="18.0" />
                        </font>
                        <cursor>
                           <Cursor fx:constant="HAND" />
                        </cursor>
                     </Button>
                     <Button fx:id="cancelOrderBtn" layoutX="69.0" layoutY="71.0" mnemonicParsing="false" prefHeight="56.0" prefWidth="211.0" style="-fx-background-color: white; -fx-background-radius: 10;" text="Cancel order">
                        <font>
                           <Font size="18.0" />
                        </font>
                        <cursor>
                           <Cursor fx:constant="HAND" />
                        </cursor>
                     </Button>
                  </children>
                  <VBox.margin>
                     <Insets top="20.0" />
                  </VBox.margin>
               </Pane>
                </children>
            <BorderPane.margin>
               <Insets right="40.0" />
            </BorderPane.margin>
            </VBox>
        </right>
    </BorderPane>
   <AnchorPane fx:id="loadingOverlay" layoutX="10.0" layoutY="10.0" style="-fx-background-color: rgba(0,0,0,0.3);" visible="false" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
      <children>
         <ProgressIndicator fx:id="loadingProgress" minHeight="100.0" minWidth="100.0" AnchorPane.bottomAnchor="200.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="200.0" />
      </children>
   </AnchorPane>
</AnchorPane>
