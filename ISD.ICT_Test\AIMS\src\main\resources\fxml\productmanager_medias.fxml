<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>


<AnchorPane fx:id="main_form" prefHeight="781.0" prefWidth="1096.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.controller.productmanager.ProductManagerMediasController">
   <children>
      <AnchorPane layoutX="58.0" layoutY="135.0" prefHeight="600.0" prefWidth="879.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <VBox prefHeight="200.0" prefWidth="100.0" spacing="20.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <AnchorPane fx:id="medias_form" prefHeight="288.0" prefWidth="853.0">
                     <children>
                        <TableView fx:id="medias_tableView" layoutX="10.0" layoutY="6.0" maxWidth="1.7976931348623157E308" onMouseClicked="#selectMedia" prefHeight="276.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                          <columns>
                            <TableColumn fx:id="medias_col_id" prefWidth="46.0" text="ID" />
                              <TableColumn fx:id="medias_col_title" prefWidth="165.0" text="Title" />
                              <TableColumn fx:id="medias_col_category" minWidth="2.0" prefWidth="85.0" text="Category" />
                              <TableColumn fx:id="medias_col_price" prefWidth="109.0" text="Price" />
                              <TableColumn fx:id="medias_col_quantity" prefWidth="68.0" text="Quantity" />
                              <TableColumn fx:id="medias_col_weight" prefWidth="68.0" text="Weight" />
                              <TableColumn fx:id="medias_col_importDate" prefWidth="113.0" text="Import date" />
                              <TableColumn fx:id="medias_col_rushOrderSupport" prefWidth="88.0" text="ROS" />
                              <TableColumn fx:id="medias_col_barcode" prefWidth="95.0" text="Barcode" />
                              <TableColumn fx:id="medias_col_productDimension" prefWidth="98.0" text="Dimension" />
                              <TableColumn fx:id="medias_col_desciption" prefWidth="270.0" text="Description" />
                          </columns>
                           <columnResizePolicy>
                              <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                           </columnResizePolicy>
                        </TableView>
                     </children>
                  </AnchorPane>
                  <HBox>
                     <children>
                        <AnchorPane prefHeight="355.0" prefWidth="934.0">
                           <children>
                              <Label layoutX="51.0" layoutY="31.0" text="Title" AnchorPane.leftAnchor="51.0" AnchorPane.topAnchor="31.0">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <TextField fx:id="media_title" layoutX="190.0" layoutY="28.0" prefHeight="26.0" prefWidth="212.0" AnchorPane.leftAnchor="190.0" AnchorPane.topAnchor="28.0" />
                              <Label layoutX="51.0" layoutY="91.0" text="Category" AnchorPane.leftAnchor="51.0">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <Label layoutX="454.0" layoutY="89.0" text="Price (VND)" AnchorPane.leftAnchor="454.0">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <TextField fx:id="media_price" layoutX="542.0" layoutY="87.0" prefHeight="26.0" prefWidth="77.0" promptText="100.000" AnchorPane.leftAnchor="542.0" AnchorPane.topAnchor="87.0" />
                              <Label layoutX="458.0" layoutY="29.0" text="Quantity" AnchorPane.leftAnchor="458.0" AnchorPane.topAnchor="29.0">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <TextField fx:id="media_quantity" layoutX="542.0" layoutY="28.0" prefHeight="26.0" prefWidth="76.0" promptText="0" AnchorPane.leftAnchor="542.0" AnchorPane.topAnchor="28.0" />
                              <Label layoutX="454.0" layoutY="203.0" text="Description" AnchorPane.leftAnchor="454.0" AnchorPane.topAnchor="203.0">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <ComboBox fx:id="media_category" layoutX="190.0" layoutY="87.0" prefHeight="26.0" prefWidth="216.0" promptText="Choose category" AnchorPane.leftAnchor="190.0" AnchorPane.topAnchor="87.0" />
                              <TextArea fx:id="media_description" layoutX="542.0" layoutY="195.0" prefHeight="165.0" prefWidth="346.0" AnchorPane.leftAnchor="542.0" />
                              <Label layoutX="51.0" layoutY="144.0" text="Rush order support" AnchorPane.leftAnchor="51.0">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <ComboBox fx:id="media_rushOrderSupport" layoutX="190.0" layoutY="141.0" prefHeight="26.0" prefWidth="216.0" promptText="Choose" AnchorPane.leftAnchor="190.0" />
                              <Label layoutX="51.0" layoutY="204.0" text="Barcode" AnchorPane.leftAnchor="51.0">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <TextField fx:id="media_barcode" layoutX="190.0" layoutY="201.0" prefHeight="26.0" prefWidth="216.0" AnchorPane.leftAnchor="190.0" />
                              <Label layoutX="458.0" layoutY="143.0" text="Dimension" AnchorPane.leftAnchor="458.0">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <TextField fx:id="media_productDimension" layoutX="542.0" layoutY="140.0" prefHeight="26.0" prefWidth="346.0" />
                              <Label layoutX="51.0" layoutY="261.0" text="Weight (KG)">
                                 <font>
                                    <Font size="15.0" />
                                 </font>
                              </Label>
                              <TextField fx:id="media_weight" layoutX="189.0" layoutY="259.0" prefHeight="26.0" prefWidth="74.0" promptText="0.0" />
                           </children>
                        </AnchorPane>
                        <VBox alignment="TOP_CENTER" spacing="20.0" HBox.hgrow="NEVER">
                           <children>
                              <AnchorPane prefHeight="150.0" prefWidth="150.0" style="-fx-background-color: d4d4d4;">
                                 <children>
                                    <ImageView fx:id="medias_imageView" fitHeight="150.0" fitWidth="150.0" layoutX="6.0" pickOnBounds="true" preserveRatio="true" />
                                 </children>
                              </AnchorPane>
                              <Button fx:id="media_importBtn" mnemonicParsing="false" onAction="#mediasImportBtn" prefHeight="30.0" prefWidth="100.0" text="Import" />
                           </children>
                           <HBox.margin>
                              <Insets bottom="20.0" left="30.0" right="30.0" top="20.0" />
                           </HBox.margin>
                        </VBox>
                     </children>
                  </HBox>
                  <HBox alignment="CENTER" minHeight="-Infinity" prefHeight="80.0" prefWidth="1090.0" spacing="20.0">
                     <children>
                        <Button fx:id="medias_addBtn" mnemonicParsing="false" onAction="#mediasAddBtn" prefHeight="30.0" prefWidth="100.0" text="Add" />
                        <Button fx:id="medias_updateBtn" mnemonicParsing="false" onAction="#mediaUpdateBtn" prefHeight="30.0" prefWidth="100.0" text="Update" />
                        <Button fx:id="medias_clearBtn" mnemonicParsing="false" onAction="#mediasClearBtn" prefHeight="30.0" prefWidth="100.0" text="Clear" />
                        <Button fx:id="medias_deleteBtn" mnemonicParsing="false" onAction="#mediaDeleteBtn" prefHeight="30.0" prefWidth="100.0" text="Delete" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </children>
      </AnchorPane>
   </children>
</AnchorPane>
